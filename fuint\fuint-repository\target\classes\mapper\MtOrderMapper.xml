<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtOrderMapper">
    <select id="getOrderCount" resultType="java.math.BigDecimal">
        select count(*) from mt_order t where t.STATUS not in('C', 'G', 'H')
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getStoreOrderCount" resultType="java.math.BigDecimal">
        select count(*) from mt_order t where t.STORE_ID = #{storeId} and t.STATUS not in('C', 'G', 'H')
    </select>

    <select id="getOrderCountByTime" resultType="java.math.BigDecimal">
        select count(0) from mt_order t where t.CREATE_TIME &gt;= #{beginTime} and t.CREATE_TIME &lt; #{endTime} and t.STATUS not in('C', 'G', 'H')
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getStoreOrderCountByTime" resultType="java.math.BigDecimal">
        select count(0) from mt_order t where t.STORE_ID = #{storeId} and t.CREATE_TIME &lt; #{endTime} and t.CREATE_TIME &gt;= #{beginTime} and t.STATUS not in('C', 'G', 'H')
    </select>

    <select id="findByOrderSn" resultType="com.fuint.repository.model.MtOrder">
        select * from mt_order o where o.ORDER_SN = #{orderSn}
    </select>

    <select id="getPayMoney" resultType="java.math.BigDecimal">
        SELECT sum(t.PAY_AMOUNT) as num FROM mt_order t where t.PAY_STATUS='B' and t.STATUS not in('C', 'G', 'H')
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getPayMoneyByTime" resultType="java.math.BigDecimal">
        SELECT sum(t.PAY_AMOUNT) as num FROM mt_order t where t.PAY_STATUS='B' and t.CREATE_TIME &lt; #{endTime} and t.CREATE_TIME &gt;= #{beginTime} and t.STATUS not in('C', 'G', 'H')
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getStorePayMoneyByTime" resultType="java.math.BigDecimal">
        SELECT sum(t.PAY_AMOUNT) as num FROM mt_order t where t.STORE_ID = #{storeId} and t.PAY_STATUS='B' and t.CREATE_TIME &lt; #{endTime} and t.CREATE_TIME &gt;= #{beginTime} and t.STATUS not in('C', 'G', 'H')
    </select>

    <select id="getStorePayMoney" resultType="java.math.BigDecimal">
        SELECT sum(t.PAY_AMOUNT) as num FROM mt_order t where t.STORE_ID = #{storeId} and t.PAY_STATUS='B' and t.STATUS not in('C', 'G', 'H')
    </select>

    <select id="getPayUserCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT t.USER_ID) as num FROM mt_order t where t.PAY_STATUS='B' and t.STATUS not in('C', 'G', 'H')
        <if test="merchantId != null and merchantId > 0">
            and t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getStorePayUserCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT t.USER_ID) as num FROM mt_order t where t.STORE_ID = #{storeId} and t.PAY_STATUS='B' and t.STATUS not in('C', 'G', 'H')
    </select>

    <select id="getUserPayMoney" resultType="java.math.BigDecimal">
        SELECT sum(t.PAY_AMOUNT) as num FROM mt_order t where t.USER_ID = #{userId} and t.PAY_STATUS='B' and t.STATUS not in('C', 'G', 'H')
    </select>

    <select id="getUserPayOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT t.ID) as num FROM mt_order t where t.USER_ID = #{userId} and t.PAY_STATUS='B' and t.STATUS not in('C', 'G', 'H')
    </select>

    <select id="getTobeCommissionOrderList" resultType="com.fuint.repository.model.MtOrder">
        SELECT t.* FROM `mt_order` t WHERE t.PAY_TIME &lt;= #{dateTime} AND t.STATUS NOT IN('C', 'G', 'H') AND t.PAY_STATUS = 'B' AND t.COMMISSION_STATUS = 'A' ORDER BY t.ID DESC LIMIT 10
    </select>
</mapper>
