<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <!-- 配置本地仓库路径 -->
  <!-- <localRepository>/path/to/local/repo</localRepository> -->

  <!-- 配置代理 如果需要 -->
  <!-- <proxies>
    <proxy>
      <id>optional</id>
      <active>true</active>
      <protocol>http</protocol>
      <host>proxy.host.com</host>
      <port>8080</port>
      <nonProxyHosts>localhost|127.0.0.1</nonProxyHosts>
    </proxy>
  </proxies> -->

  <!-- 服务器认证配置 -->
  <!-- <servers>
    <server>
      <id>deploymentRepo</id>
      <username>username</username>
      <password>password</password>
    </server>
  </servers> -->

  <!-- Maven镜像配置 - 解决依赖下载问题 -->
  <mirrors>
    <!-- 阿里云公共仓库 -->
    <mirror>
      <id>aliyunmaven</id>
      <mirrorOf>*</mirrorOf>
      <name>阿里云公共仓库</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </mirror>
    
    <!-- 阿里云central仓库 -->
    <mirror>
      <id>aliyunmavencentral</id>
      <mirrorOf>central</mirrorOf>
      <name>阿里云central仓库</name>
      <url>https://maven.aliyun.com/repository/central</url>
    </mirror>
    
    <!-- 阿里云spring仓库 -->
    <mirror>
      <id>aliyunmavenspring</id>
      <mirrorOf>spring</mirrorOf>
      <name>阿里云spring仓库</name>
      <url>https://maven.aliyun.com/repository/spring</url>
    </mirror>
  </mirrors>

  <!-- 配置Maven连接超时参数 -->
  <httpConnector>
    <connectionTimeout>60000</connectionTimeout><!-- 毫秒 -->
    <readTimeout>60000</readTimeout><!-- 毫秒 -->
  </httpConnector>

  <!-- 配置文件激活 -->
  <profiles>
    <profile>
      <id>jdk-1.8</id>
      <activation>
        <activeByDefault>true</activeByDefault>
        <jdk>1.8</jdk>
      </activation>
      <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
      </properties>
    </profile>
  </profiles>

</settings>