<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtGiveMapper">
    <select id="queryForUnique" resultType="com.fuint.repository.model.MtGive">
        SELECT * FROM mt_give t WHERE t.USER_ID = #{userId} AND t.GIVE_USER_ID = #{giveUserId} AND t.COUPON_IDS = #{couponIds} AND t.CREATE_TIME >= #{createTime}
    </select>
</mapper>
