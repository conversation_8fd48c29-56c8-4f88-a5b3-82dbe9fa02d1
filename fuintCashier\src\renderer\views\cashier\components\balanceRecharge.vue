<template>
  <el-dialog
    title="余额充值"
    :visible.sync="dialogVisible"
    width="500px"
    append-to-body
    @close="handleClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="充值金额" prop="amount">
        <el-input
          v-model="form.amount"
          type="number"
          placeholder="请输入充值金额"
          clearable
        />
      </el-form-item>
      <el-form-item label="充值方式" prop="payType">
        <el-select v-model="form.payType" placeholder="请选择充值方式">
          <el-option label="现金" value="cash" />
          <el-option label="微信支付" value="wechat" />
          <el-option label="支付宝" value="alipay" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'BalanceRecharge',
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        amount: '',
        payType: '',
        remark: ''
      },
      rules: {
        amount: [
          { required: true, message: '请输入充值金额', trigger: 'blur' },
          { pattern: /^[0-9]+(\.[0-9]{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        payType: [
          { required: true, message: '请选择充值方式', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.showDialog
      },
      set(val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // TODO: 调用充值接口
          console.log('充值数据:', {
            userId: this.userId,
            ...this.form
          })
          this.$message.success('充值成功')
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.$emit('closeDialog', 'balance')
      this.resetForm()
    },
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
      this.form = {
        amount: '',
        payType: '',
        remark: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
