<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.TSourceMapper">
    <select id="findSourcesByAccountId" resultType="com.fuint.repository.model.TSource">
        SELECT s.* FROM `t_source` s WHERE s.status='A'
        AND (s.merchant_id = 0 OR s.merchant_id = #{merchantId})
        AND source_id IN(SELECT source_id FROM `t_duty_source` WHERE duty_id IN(SELECT duty_id FROM `t_account_duty` WHERE acct_id = #{accountId}))
        ORDER BY s.source_style ASC
        LIMIT 1000
    </select>

    <select id="findByIdIn" resultType="com.fuint.repository.model.TSource">
        select * from t_source where source_id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        order by source_style asc
    </select>

    <select id="findByStatus" resultType="com.fuint.repository.model.TSource">
        select * from t_source s
        where s.status != 'D'
        <if test="status != null and status != ''">
            and s.status = #{status}
        </if>
        <if test="merchantId != null and merchantId > 0">
            and (s.merchant_id = 0 OR s.merchant_id = #{merchantId})
        </if>
        order by source_style asc
    </select>
</mapper>
