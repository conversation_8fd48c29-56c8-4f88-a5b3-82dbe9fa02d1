<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtUserMapper">
    <select id="queryMemberByMobile" resultType="com.fuint.repository.model.MtUser">
        select * from mt_user t where t.MOBILE = #{mobile} AND t.status != 'D'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="queryMemberByName" resultType="com.fuint.repository.model.MtUser">
        select * from mt_user t where t.NAME = #{name} and t.STATUS = 'A'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="queryMemberByOpenId" resultType="com.fuint.repository.model.MtUser">
        select * from mt_user t where t.OPEN_ID = #{openId} and t.STATUS != 'D'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
        limit 1
    </select>

    <select id="findMembersByUserNo" resultType="com.fuint.repository.model.MtUser">
        select * from mt_user t where t.USER_NO = #{userNo} and t.STATUS = 'A'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
        limit 1
    </select>

    <update id="updateActiveTime">
        update mt_user t set t.UPDATE_TIME = #{updateTime} where t.ID = #{userId}
    </update>

    <update id="updateUserBalance">
        update mt_user t set t.BALANCE = (t.BALANCE + #{amount}) where t.STATUS = 'A'
        <if test="userIds != null and userIds.size > 0">
            AND t.ID IN
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </update>

    <update id="resetMobile">
        update mt_user t set t.MOBILE = '' where t.MOBILE = #{mobile} and t.ID != #{userId}
    </update>

    <select id="getUserCount" resultType="java.lang.Long">
        select count(*) from mt_user t where t.STATUS != 'D' and t.IS_STAFF = 'N'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getStoreUserCount" resultType="java.lang.Long">
        select count(*) from mt_user t where t.STORE_ID = #{storeId} and t.STATUS != 'D' and t.IS_STAFF = 'N'
    </select>

    <select id="getUserCountByTime" resultType="java.lang.Long">
        select count(*) from mt_user t where t.CREATE_TIME &gt;= #{beginTime} and t.CREATE_TIME &lt; #{endTime} and t.STATUS != 'D' and t.IS_STAFF = 'N'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getStoreUserCountByTime" resultType="java.lang.Long">
        select count(*) from mt_user t where t.STORE_ID = #{storeId} and t.CREATE_TIME &lt; #{endTime} and t.CREATE_TIME &gt;= #{beginTime} and t.STATUS != 'D' and t.IS_STAFF = 'N'
    </select>

    <select id="getMemberConsumeTopList" resultType="com.fuint.repository.bean.MemberTopBean">
        SELECT u.id AS ID,u.name AS NAME,u.`USER_NO`,SUM(o.`AMOUNT`) AS amount FROM `mt_user` u,`mt_order` o WHERE u.`ID` = o.`USER_ID` AND u.STATUS = 'A' AND u.IS_STAFF = 'N' AND o.STATUS != 'G' AND o.PAY_STATUS = 'B'
        <if test="merchantId != null and merchantId > 0">
            AND u.MERCHANT_ID = #{merchantId}
        </if>
        <if test="storeId != null and storeId > 0">
            AND u.STORE_ID = #{storeId}
        </if>
        <if test="startTime != null">
            AND o.CREATE_TIME &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND o.CREATE_TIME &lt;= #{endTime}
        </if>
        GROUP BY u.`ID` ORDER BY SUM(o.`AMOUNT`) DESC LIMIT 10
    </select>

    <select id="getUserIdList" resultType="java.lang.Integer">
        select t.ID from mt_user t where t.STATUS = 'A'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
        <if test="storeId != null and storeId > 0">
            AND t.STORE_ID = #{storeId}
        </if>
        limit 50000;
    </select>

    <select id="searchMembers" resultType="com.fuint.repository.model.MtUser">
        select * from mt_user t where (t.MOBILE like concat('%',#{keyword},'%') or t.NAME like concat('%',#{keyword},'%')) and t.STATUS = 'A'
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
        limit 10
    </select>

</mapper>
