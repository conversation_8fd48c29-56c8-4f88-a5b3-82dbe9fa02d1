<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtSendLogMapper">
    <update id="updateForRemove">
        update mt_send_log p set p.STATUS =#{status},p.REMOVE_SUCCESS_NUM =#{removeSuccessNum},p.REMOVE_FAIL_NUM =#{removeFailNum} where p.UUID = #{uuid}
    </update>

    <update id="updateSingleForRemove">
        update mt_send_log p set p.STATUS =#{status},p.REMOVE_SUCCESS_NUM =p.REMOVE_SUCCESS_NUM+1 where p.UUID =#{uuid}
    </update>
</mapper>
