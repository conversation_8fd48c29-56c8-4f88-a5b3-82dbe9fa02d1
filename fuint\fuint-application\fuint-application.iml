<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: com.fuint:fuint-util:1.0.0-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jcl-over-slf4j:1.7.18" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: org.logback-extensions:logback-ext-spring:0.1.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.3.2" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.10" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.17" level="project" />
    <orderEntry type="library" name="Maven: javax.servlet:javax.servlet-api:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: cglib:cglib:3.2.4" level="project" />
    <orderEntry type="library" name="Maven: org.ow2.asm:asm:6.0_ALPHA" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ant:ant:1.9.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ant:ant-launcher:1.9.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven.plugins:maven-compiler-plugin:2.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-plugin-api:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-artifact:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-core:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-settings:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-plugin-parameter-documenter:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-profile:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-model:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven.wagon:wagon-provider-api:1.0-beta-2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-repository-metadata:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-error-diagnostics:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-project:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-plugin-registry:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-plugin-descriptor:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-artifact-manager:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-monitor:2.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.apache.maven:maven-toolchain:1.0" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.plexus:plexus-utils:2.0.5" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.plexus:plexus-compiler-api:1.8.1" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.plexus:plexus-compiler-manager:1.8.1" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.codehaus.plexus:plexus-compiler-javac:1.8.1" level="project" />
    <orderEntry type="library" name="Maven: com.fuint:fuint-core:1.0.0-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: aopalliance:aopalliance:1.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aspects:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.8.9" level="project" />
    <orderEntry type="library" name="Maven: commons-fileupload:commons-fileupload:1.3.1" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: mysql:mysql-connector-java:8.0.11" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: com.google.protobuf:protobuf-java:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.7.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.7.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.7.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.7.5" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.woodstox:stax2-api:3.1.4" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.7.5" level="project" />
    <orderEntry type="library" name="Maven: org.freemarker:freemarker:2.3.23" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fuint:fuint-component:1.0.0-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:4.2.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fuint:fuint-cache:1.0.0-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: redis.clients:jedis:2.9.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-pool2:2.4.2" level="project" />
    <orderEntry type="library" name="Maven: org.redisson:redisson:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: javax.cache:cache-api:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-common:4.0.42.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec:4.0.42.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-buffer:4.0.42.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport:4.0.42.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-handler:4.0.42.Final" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-stream:2.0.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:2.0.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.7.6" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.15" level="project" />
    <orderEntry type="library" name="Maven: net.openhft:zero-allocation-hashing:0.5" level="project" />
    <orderEntry type="library" name="Maven: net.bytebuddy:byte-buddy:1.4.26" level="project" />
    <orderEntry type="library" name="Maven: org.jodd:jodd-bean:3.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.jodd:jodd-core:3.7.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.axet:kaptcha:0.0.9" level="project" />
    <orderEntry type="library" name="Maven: com.jhlabs:filters:2.0.235" level="project" />
  </component>
</module>