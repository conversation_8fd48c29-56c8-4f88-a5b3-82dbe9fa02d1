<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtGoodsSpecMapper">
    <select id="getGoodsSpecCountList" resultType="com.fuint.repository.model.MtGoodsSpec">
        SELECT ANY_VALUE(`ID`) AS ID,ANY_VALUE(`GOODS_ID`) AS GOODS_ID,ANY_VALUE(`NAME`) AS NAME,ANY_VALUE(`VALUE`) AS VALUE,ANY_VALUE(`STATUS`) AS STATUS FROM mt_goods_spec t WHERE t.GOODS_ID = #{goodsId} AND t.STATUS = 'A' GROUP BY t.name
    </select>
</mapper>
