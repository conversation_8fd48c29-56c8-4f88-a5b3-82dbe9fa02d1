package com.fuint.repository.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * ${moduleName}实体
 *
 * @Created by ${author}
 * CopyRight https://www.fuint.cn
 */
@Getter
@Setter
@TableName("${tablePrefix}_${tableName}")
@ApiModel(value = "${tableName}表对象", description = "${tableName}表对象")
public class ${modelName} implements Serializable {

    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
    @ApiModelProperty("$column.comment")
    #if($column.field == 'id')
    @TableId(value = "ID", type = IdType.AUTO)
    #end
    private $column.type $column.field;

#end
}
