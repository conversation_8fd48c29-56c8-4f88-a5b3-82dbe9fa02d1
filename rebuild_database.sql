-- fuint数据库重建脚本
-- 删除现有数据库
DROP DATABASE IF EXISTS `fuint-db`;

-- 创建新数据库
CREATE DATABASE `fuint-db` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;

-- 使用数据库
USE `fuint-db`;

-- 导入基础表结构（从fuint/db/fuint-db.sql）
-- 这里只包含关键的会员相关表，完整的表结构请参考fuint/db/fuint-db.sql

/*Table structure for table `mt_user` */
DROP TABLE IF EXISTS `mt_user`;

CREATE TABLE `mt_user` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `MOBILE` varchar(20) DEFAULT '' COMMENT '手机号码',
  `GROUP_ID` int DEFAULT '0' COMMENT '分组ID',
  `USER_NO` varchar(30) DEFAULT '' COMMENT '会员号',
  `AVATAR` varchar(255) DEFAULT '' COMMENT '头像',
  `NAME` varchar(30) DEFAULT '' COMMENT '称呼',
  `OPEN_ID` varchar(50) DEFAULT '' COMMENT '微信open_id',
  `IDCARD` varchar(20) DEFAULT '' COMMENT '证件号码',
  `GRADE_ID` varchar(10) DEFAULT '1' COMMENT '等级ID',
  `START_TIME` datetime DEFAULT NULL COMMENT '会员开始时间',
  `END_TIME` datetime DEFAULT NULL COMMENT '会员结束时间',
  `BALANCE` float(10,2) DEFAULT '0.00' COMMENT '余额',
  `POINT` int DEFAULT '0' COMMENT '积分',
  `SEX` int DEFAULT '1' COMMENT '性别 1男；0女',
  `BIRTHDAY` varchar(20) DEFAULT '' COMMENT '出生日期',
  `CAR_NO` varchar(10) DEFAULT '' COMMENT '车牌号',
  `SOURCE` varchar(30) DEFAULT '' COMMENT '来源渠道',
  `PASSWORD` varchar(32) DEFAULT '' COMMENT '密码',
  `SALT` varchar(4) DEFAULT '' COMMENT 'salt',
  `ADDRESS` varchar(100) DEFAULT '' COMMENT '地址',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `IS_STAFF` char(1) DEFAULT 'N' COMMENT '是否员工',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：激活；N：禁用；D：删除',
  `DESCRIPTION` varchar(255) DEFAULT '' COMMENT '备注信息',
  `IP` varchar(20) DEFAULT '' COMMENT '注册IP',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`),
  KEY `index_phone` (`MOBILE`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='会员个人信息';

/*Table structure for table `mt_user_grade` */
DROP TABLE IF EXISTS `mt_user_grade`;

CREATE TABLE `mt_user_grade` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `GRADE` tinyint DEFAULT '1' COMMENT '等级',
  `NAME` varchar(30) DEFAULT '' COMMENT '等级名称',
  `CATCH_CONDITION` varchar(255) DEFAULT '' COMMENT '升级会员等级条件描述',
  `CATCH_TYPE` varchar(30) DEFAULT 'pay' COMMENT '升级会员等级条件，init:默认获取;pay:付费升级；frequency:消费次数；amount:累积消费金额升级',
  `CATCH_VALUE` float(10,2) DEFAULT '0.00' COMMENT '达到升级条件的值',
  `USER_PRIVILEGE` varchar(1000) DEFAULT '' COMMENT '会员权益描述',
  `VALID_DAY` int DEFAULT '0' COMMENT '有效期',
  `DISCOUNT` float(5,2) DEFAULT '0.00' COMMENT '享受折扣',
  `SPEED_POINT` float(5,2) DEFAULT '1.00' COMMENT '积分加速',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COMMENT='会员等级表';

/*Table structure for table `t_account` */
DROP TABLE IF EXISTS `t_account`;

CREATE TABLE `t_account` (
  `acct_id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `account_key` varchar(23) NOT NULL DEFAULT '' COMMENT '账户编码',
  `account_name` varchar(20) NOT NULL DEFAULT '' COMMENT '账户名称',
  `password` varchar(100) NOT NULL DEFAULT '' COMMENT '密码',
  `account_status` int NOT NULL DEFAULT '1' COMMENT '0 无效 1 有效',
  `is_active` int NOT NULL DEFAULT '0' COMMENT '0 未激活 1已激活',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `modify_date` datetime NOT NULL COMMENT '修改时间',
  `salt` varchar(64) NOT NULL DEFAULT '' COMMENT '随机码',
  `role_ids` varchar(100) DEFAULT NULL COMMENT '角色ID',
  `locked` int NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `owner_id` int DEFAULT NULL COMMENT '所属平台',
  `real_name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `merchant_id` int DEFAULT '0' COMMENT '所属商户ID',
  `store_id` int DEFAULT '0' COMMENT '所属店铺ID',
  `staff_id` int DEFAULT '0' COMMENT '关联员工ID',
  PRIMARY KEY (`acct_id`),
  KEY `FKmlsqc08c6khxhoed7abkl2s9l` (`owner_id`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- 插入测试会员数据
INSERT INTO `mt_user` (`ID`, `MOBILE`, `USER_NO`, `NAME`, `GRADE_ID`, `BALANCE`, `POINT`, `SEX`, `MERCHANT_ID`, `STORE_ID`, `CREATE_TIME`, `UPDATE_TIME`, `STATUS`, `OPERATOR`) VALUES
(1, '13800138001', 'M001', '张三', '1', 100.00, 500, 1, 1, 1, NOW(), NOW(), 'A', 'system'),
(2, '13800138002', 'M002', '李四', '1', 200.50, 800, 0, 1, 1, NOW(), NOW(), 'A', 'system'),
(3, '13800138003', 'M003', '王五', '2', 50.00, 300, 1, 1, 1, NOW(), NOW(), 'A', 'system'),
(4, '13800138004', 'M004', '赵六', '1', 0.00, 100, 0, 1, 1, NOW(), NOW(), 'A', 'system');

-- 插入会员等级数据
INSERT INTO `mt_user_grade` (`ID`, `MERCHANT_ID`, `GRADE`, `NAME`, `CATCH_CONDITION`, `CATCH_TYPE`, `CATCH_VALUE`, `USER_PRIVILEGE`, `VALID_DAY`, `DISCOUNT`, `SPEED_POINT`, `STATUS`) VALUES
(1, 0, 1, '普通会员', '注册即可获得', 'init', 0.00, '享受会员价格', 0, 1.00, 1.00, 'A'),
(2, 0, 2, '银卡会员', '累计消费满500元', 'amount', 500.00, '享受9.5折优惠', 365, 0.95, 1.20, 'A'),
(3, 0, 3, '金卡会员', '累计消费满1000元', 'amount', 1000.00, '享受9折优惠', 365, 0.90, 1.50, 'A'),
(4, 0, 4, 'VIP会员', '累计消费满2000元', 'amount', 2000.00, '享受8.5折优惠', 365, 0.85, 2.00, 'A');

-- 插入管理员账户
INSERT INTO `t_account` (`acct_id`, `account_key`, `account_name`, `password`, `account_status`, `is_active`, `create_date`, `modify_date`, `salt`, `role_ids`, `locked`, `owner_id`, `real_name`, `merchant_id`, `store_id`, `staff_id`) VALUES
(1, 'admin', 'admin', 'e10adc3949ba59abbe56e057f20f883e', 1, 1, NOW(), NOW(), 'salt', '1', 0, 1, '系统管理员', 1, 1, 0);

-- 创建商户表
DROP TABLE IF EXISTS `mt_merchant`;
CREATE TABLE `mt_merchant` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `NAME` varchar(100) NOT NULL DEFAULT '' COMMENT '商户名称',
  `NO` varchar(30) DEFAULT '' COMMENT '商户号',
  `LOGO` varchar(200) DEFAULT '' COMMENT '商户logo',
  `CONTACT` varchar(30) DEFAULT '' COMMENT '联系人',
  `PHONE` varchar(20) DEFAULT '' COMMENT '联系电话',
  `ADDRESS` varchar(255) DEFAULT '' COMMENT '联系地址',
  `DESCRIPTION` varchar(2000) DEFAULT '' COMMENT '备注',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='商户表';

-- 插入默认商户
INSERT INTO `mt_merchant` (`ID`, `NAME`, `NO`, `CONTACT`, `PHONE`, `ADDRESS`, `CREATE_TIME`, `UPDATE_TIME`, `STATUS`, `OPERATOR`) VALUES
(1, '测试商户', 'M001', '管理员', '************', '测试地址', NOW(), NOW(), 'A', 'system');

-- 创建店铺表
DROP TABLE IF EXISTS `mt_store`;
CREATE TABLE `mt_store` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int unsigned DEFAULT '0' COMMENT '所属商户',
  `NAME` varchar(50) NOT NULL DEFAULT '' COMMENT '店铺名称',
  `QR_CODE` varchar(255) DEFAULT '' COMMENT '店铺二维码',
  `LOGO` varchar(100) DEFAULT '' COMMENT '店铺LOGO',
  `IS_DEFAULT` char(1) NOT NULL DEFAULT 'N' COMMENT '是否默认',
  `CONTACT` varchar(30) DEFAULT '' COMMENT '联系人姓名',
  `PHONE` varchar(20) DEFAULT '' COMMENT '联系电话',
  `ADDRESS` varchar(100) DEFAULT '' COMMENT '联系地址',
  `HOURS` varchar(100) DEFAULT '' COMMENT '营业时间',
  `LATITUDE` varchar(20) DEFAULT '' COMMENT '纬度',
  `LONGITUDE` varchar(20) DEFAULT '' COMMENT '经度',
  `DISTANCE` varchar(20) DEFAULT '' COMMENT '距离',
  `WX_MCH_ID` varchar(30) DEFAULT '' COMMENT '微信支付商户号',
  `WX_API_V2` varchar(32) DEFAULT '' COMMENT '微信支付APIv2密钥',
  `WX_CERT_PATH` varchar(255) DEFAULT '' COMMENT '微信支付证书',
  `ALIPAY_APP_ID` varchar(100) DEFAULT '' COMMENT '支付宝appId',
  `ALIPAY_PRIVATE_KEY` varchar(5000) DEFAULT '' COMMENT '支付宝应用私钥',
  `ALIPAY_PUBLIC_KEY` varchar(5000) DEFAULT '' COMMENT '支付宝应用公钥',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  `DESCRIPTION` varchar(255) DEFAULT '' COMMENT '备注',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='店铺表';

-- 插入默认店铺
INSERT INTO `mt_store` (`ID`, `MERCHANT_ID`, `NAME`, `IS_DEFAULT`, `CONTACT`, `PHONE`, `ADDRESS`, `HOURS`, `CREATE_TIME`, `UPDATE_TIME`, `STATUS`, `OPERATOR`) VALUES
(1, 1, '总店', 'Y', '店长', '************', '测试地址', '09:00-21:00', NOW(), NOW(), 'A', 'system');
