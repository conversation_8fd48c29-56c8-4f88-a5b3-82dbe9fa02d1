# 基本配置
server.port=8080
env.profile=dev
env.properties.path=D:/kaifa/fuint/fuint/configure

# 数据库配置
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.cache.type=ehcache
spring.cache.ehcache.config=classpath:ehcache.xml

# 日志级别
logging.level.com.fuint=info
multipart.max-file-size=20mb
multipart.max-request-size=20mb

# 最大上传文件
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# mybatis配置
mybatis-plus.mapper-locations = classpath*:/mapper/*.xml

# 默认时间格式
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

# 数据库日志
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
