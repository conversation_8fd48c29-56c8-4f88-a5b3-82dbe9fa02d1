<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtSettingMapper">
    <select id="querySettingByType" resultType="com.fuint.repository.model.MtSetting">
        select * from mt_setting t where t.TYPE = #{type} and t.MERCHANT_ID = #{merchantId} and t.status='A'
    </select>

    <select id="querySettingByName" resultType="com.fuint.repository.model.MtSetting">
        select * from mt_setting t where t.NAME = #{name} AND t.TYPE = #{type} AND t.MERCHANT_ID = #{merchantId}
        <if test="storeId != null and storeId > 0">
          AND (t.STORE_ID = #{storeId} OR t.STORE_ID = 0)
        </if>
          AND t.STATUS='A' LIMIT 1
    </select>
</mapper>
