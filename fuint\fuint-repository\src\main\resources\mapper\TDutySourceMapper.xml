<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.TDutySourceMapper">
    <select id="findSourceIdsByDutyId" resultType="java.lang.Long">
        select distinct t.source_id from t_duty_source t
        where t.duty_id = #{dutyId}
    </select>

    <delete id="deleteSourcesByDutyId">
        delete from t_duty_source where duty_id = #{dutyId}
    </delete>
</mapper>
