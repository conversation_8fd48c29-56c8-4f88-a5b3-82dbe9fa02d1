<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtVerifyCodeMapper">
    <select id="queryByMobileVerifyCode" resultType="com.fuint.repository.model.MtVerifyCode">
        select * from mt_verify_code t where t.MOBILE = #{mobile} and t.VERIFY_CODE = #{verifyCode} and t.VALID_FLAG = 0 and t.EXPIRE_TIME >= #{queryTime}
    </select>

    <select id="queryVerifyCodeLastRecord" resultType="com.fuint.repository.model.MtVerifyCode">
        select * from mt_verify_code t where t.MOBILE = #{mobile}
    </select>
</mapper>
