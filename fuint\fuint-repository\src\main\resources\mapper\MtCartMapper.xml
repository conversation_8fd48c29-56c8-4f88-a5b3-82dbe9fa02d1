<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtCartMapper">
    <delete id="deleteCartItem">
        delete from mt_cart where USER_ID = #{userId} and GOODS_ID = #{goodsId} and SKU_ID = #{skuId}
    </delete>

    <delete id="clearCart">
        delete from mt_cart where USER_ID = #{userId}
    </delete>

    <delete id="deleteCartByHangNo">
        delete from mt_cart where HANG_NO = #{hangNo}
    </delete>
</mapper>
