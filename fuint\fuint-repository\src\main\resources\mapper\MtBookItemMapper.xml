<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtBookItemMapper">
    <select id="getBookList" resultType="java.lang.String">
        SELECT SERVICE_TIME FROM mt_book_item t where t.BOOK_ID = #{bookId}
        <if test="date != null and date != ''">
            and t.SERVICE_DATE = #{date}
        </if>
        <if test="time != null and time != ''">
            and t.SERVICE_TIME = #{time}
        </if>
            and t.STATUS !='D'
    </select>
</mapper>
