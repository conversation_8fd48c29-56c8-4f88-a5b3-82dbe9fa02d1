# Fuint系统Linux环境启动脚本

本文档提供了在Linux环境下启动和管理Fuint系统的脚本示例，配合宝塔面板使用可以更方便地管理应用。

## 一、启动脚本 (start.sh)

```bash
#!/bin/bash

# 应用根目录
APP_HOME="/www/wwwroot/fuint"
# 应用JAR包路径
APP_JAR="${APP_HOME}/fuint-application/target/fuint-application-1.0.0.jar"
# 日志文件路径
LOG_FILE="${APP_HOME}/fuint.log"
# PID文件路径
PID_FILE="${APP_HOME}/fuint.pid"
# 环境配置
PROFILE="prod"

# JVM参数配置
JAVA_OPTS="-Xms1g -Xmx2g -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"

# 检查应用是否已经运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "应用已经在运行中，PID: $PID"
        exit 1
    else
        echo "PID文件存在但应用未运行，将删除PID文件"
        rm -f "$PID_FILE"
    fi
fi

# 启动应用
echo "正在启动Fuint应用..."
cd "$APP_HOME"
nohup java $JAVA_OPTS -jar "$APP_JAR" --spring.profiles.active=$PROFILE > "$LOG_FILE" 2>&1 &

# 保存PID
echo $! > "$PID_FILE"
echo "应用已启动，PID: $(cat $PID_FILE)"

# 检查启动状态
sleep 5
if ps -p $(cat "$PID_FILE") > /dev/null; then
    echo "应用启动成功！"
    echo "日志文件: $LOG_FILE"
    echo "可以使用 'tail -f $LOG_FILE' 查看运行日志"
else
    echo "应用启动失败，请检查日志文件: $LOG_FILE"
    exit 1
fi
```

## 二、停止脚本 (stop.sh)

```bash
#!/bin/bash

# 应用根目录
APP_HOME="/www/wwwroot/fuint"
# PID文件路径
PID_FILE="${APP_HOME}/fuint.pid"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "PID文件不存在，应用可能未运行"
    exit 0
fi

# 读取PID
PID=$(cat "$PID_FILE")

# 检查进程是否存在
if ! ps -p $PID > /dev/null; then
    echo "进程不存在，可能已经停止，将删除PID文件"
    rm -f "$PID_FILE"
    exit 0
fi

# 尝试优雅停止
echo "正在停止应用 (PID: $PID)..."
kill $PID

# 等待进程结束
TIMEOUT=30
while ps -p $PID > /dev/null && [ $TIMEOUT -gt 0 ]; do
    sleep 1
    TIMEOUT=$((TIMEOUT-1))
    echo -n "."
done

# 如果进程仍然存在，强制终止
if ps -p $PID > /dev/null; then
    echo "\n应用未能在30秒内停止，将强制终止"
    kill -9 $PID
    sleep 1
fi

# 检查进程是否已终止
if ps -p $PID > /dev/null; then
    echo "无法终止应用，请手动检查进程 $PID"
    exit 1
else
    echo "\n应用已成功停止"
    rm -f "$PID_FILE"
fi
```

## 三、重启脚本 (restart.sh)

```bash
#!/bin/bash

# 应用根目录
APP_HOME="/www/wwwroot/fuint"
# 停止脚本
STOP_SCRIPT="${APP_HOME}/stop.sh"
# 启动脚本
START_SCRIPT="${APP_HOME}/start.sh"

# 检查脚本是否存在
if [ ! -f "$STOP_SCRIPT" ] || [ ! -f "$START_SCRIPT" ]; then
    echo "停止或启动脚本不存在，请检查路径"
    exit 1
fi

# 确保脚本有执行权限
chmod +x "$STOP_SCRIPT"
chmod +x "$START_SCRIPT"

# 执行停止脚本
echo "正在停止应用..."
"$STOP_SCRIPT"

# 等待几秒确保应用完全停止
sleep 3

# 执行启动脚本
echo "正在启动应用..."
"$START_SCRIPT"
```

## 四、状态检查脚本 (status.sh)

```bash
#!/bin/bash

# 应用根目录
APP_HOME="/www/wwwroot/fuint"
# PID文件路径
PID_FILE="${APP_HOME}/fuint.pid"
# 日志文件路径
LOG_FILE="${APP_HOME}/fuint.log"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "PID文件不存在，应用可能未运行"
    exit 1
fi

# 读取PID
PID=$(cat "$PID_FILE")

# 检查进程是否存在
if ps -p $PID > /dev/null; then
    echo "应用正在运行，PID: $PID"
    echo "运行时间: $(ps -o etime= -p $PID)"
    echo "内存使用: $(ps -o %mem= -p $PID)%"
    echo "CPU使用: $(ps -o %cpu= -p $PID)%"
    echo "\n最近的日志内容:"
    tail -n 10 "$LOG_FILE"
    exit 0
else
    echo "PID文件存在但进程不存在，应用可能已异常终止"
    echo "最后的日志内容:"
    tail -n 20 "$LOG_FILE"
    echo "\n建议删除PID文件并重新启动应用"
    exit 1
fi
```

## 五、设置脚本权限

将以上脚本保存到相应文件后，需要设置执行权限：

```bash
chmod +x /www/wwwroot/fuint/start.sh
chmod +x /www/wwwroot/fuint/stop.sh
chmod +x /www/wwwroot/fuint/restart.sh
chmod +x /www/wwwroot/fuint/status.sh
```

## 六、配置宝塔面板定时任务

在宝塔面板中，可以添加以下定时任务：

1. **应用状态监控**：每5分钟检查一次应用状态，如果不在运行则自动重启

```bash
*/5 * * * * cd /www/wwwroot/fuint && ./status.sh > /dev/null || ./restart.sh >> /www/wwwroot/fuint/monitor.log 2>&1
```

2. **日志轮转**：每天凌晨1点进行日志轮转，避免日志文件过大

```bash
0 1 * * * cd /www/wwwroot/fuint && mv fuint.log fuint.log.$(date +\%Y\%m\%d) && touch fuint.log && find . -name "fuint.log.*" -mtime +7 -delete
```

3. **定期重启**：每周日凌晨3点定期重启应用，保持系统稳定

```bash
0 3 * * 0 cd /www/wwwroot/fuint && ./restart.sh >> /www/wwwroot/fuint/restart.log 2>&1
```

## 七、注意事项

1. 脚本中的路径、JVM参数等需要根据实际情况调整
2. 确保脚本有正确的执行权限
3. 定时任务的频率可以根据实际需求调整
4. 建议配置日志轮转，避免日志文件过大
5. 重要的生产环境建议配置监控和自动重启机制