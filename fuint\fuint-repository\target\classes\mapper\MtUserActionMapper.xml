<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtUserActionMapper">
    <select id="getActiveUserCount" resultType="java.lang.Long">
        SELECT count(DISTINCT t.USER_ID) as num FROM mt_user_action t where t.STATUS='A' and t.UPDATE_TIME &lt;= #{endTime} and t.UPDATE_TIME &gt;= #{beginTime}
        <if test="merchantId != null and merchantId > 0">
            AND t.MERCHANT_ID = #{merchantId}
        </if>
    </select>

    <select id="getStoreActiveUserCount" resultType="java.lang.Long">
        SELECT count(DISTINCT t.USER_ID) as num FROM mt_user_action t where t.STORE_ID = #{storeId} and t.STATUS='A' and t.UPDATE_TIME &lt;= #{endTime} and t.UPDATE_TIME &gt;= #{beginTime}
    </select>
</mapper>
