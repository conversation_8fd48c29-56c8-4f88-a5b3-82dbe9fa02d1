# Fuint系统在Linux宝塔面板环境下的部署指南

## 一、Maven构建问题解决方案

### 1. 网络连接问题

根据错误信息，Maven无法连接到中央仓库下载依赖，导致构建失败：

```
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-jar-plugin:3.2.2:jar (default-jar) on project fuint-utils: Execution default-jar of goal org.apache.maven.plugins:maven-jar-plugin:3.2.2:jar failed: Plugin org.apache.maven.plugins:maven-jar-plugin:3.2.2 or one of its dependencies could not be resolved:
[ERROR] Could not transfer artifact org.apache.maven:maven-compat:jar:3.0 from/to central (https://repo.maven.apache.org/maven2): Connect to repo.maven.apache.org:443 [repo.maven.apache.org/151.101.108.215] failed: Connect timed out
```

#### 解决方案：配置国内Maven镜像

在服务器上创建或修改Maven的settings.xml文件：

```bash
# 如果是全局配置，编辑以下文件
vi /usr/share/maven/conf/settings.xml
# 或者用户级配置
vi ~/.m2/settings.xml
```

在`<mirrors>`标签中添加阿里云Maven镜像：

```xml
<mirrors>
  <mirror>
    <id>aliyunmaven</id>
    <mirrorOf>*</mirrorOf>
    <name>阿里云公共仓库</name>
    <url>https://maven.aliyun.com/repository/public</url>
  </mirror>
</mirrors>
```

#### 增加Maven连接超时时间

在settings.xml中添加：

```xml
<settings>
  <!-- 其他配置 -->
  
  <!-- 设置超时参数 -->
  <httpConnector>
    <connectionTimeout>60000</connectionTimeout><!-- 毫秒 -->
    <readTimeout>60000</readTimeout><!-- 毫秒 -->
  </httpConnector>
</settings>
```

### 2. 缺失依赖问题

项目可能会出现以下依赖问题：

```
[ERROR] /D:/kaifa/fuint/fuint/fuint-application/src/main/java/com/fuint/common/config/WXPayConfigImpl.java:[7,28] 程序包com.github.wxpay.sdk不存在
[ERROR] /D:/kaifa/fuint/fuint/fuint-application/src/main/java/com/fuint/common/config/WXPayConfigImpl.java:[25,41] 找不到符号
[ERROR]   符号: 类 WXPayConfig
```

#### 解决方案：添加微信支付SDK依赖

在项目根目录的pom.xml文件中添加微信支付SDK依赖：

```xml
<dependency>
    <groupId>com.github.wxpay</groupId>
    <artifactId>wxpay-sdk</artifactId>
    <version>0.0.3</version>
</dependency>
```

### 3. Lombok注解处理问题

项目中使用了Lombok，但可能出现以下错误：

```
[ERROR] /D:/kaifa/fuint/fuint/fuint-application/src/main/java/com/fuint/common/service/impl/MemberServiceImpl.java:[47,21] 找不到符号
[ERROR]   符号:   方法 onConstructor_()
[ERROR]   位置: @interface lombok.AllArgsConstructor
```

#### 解决方案：更新Lombok版本

在项目根目录的pom.xml文件中更新Lombok依赖版本：

```xml
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <version>1.18.20</version>
    <scope>provided</scope>
</dependency>
```

### 4. 使用离线模式或本地依赖

如果网络问题严重，可以考虑从其他环境将依赖包下载好，然后上传到服务器的本地仓库中。

#### 手动下载并安装依赖

```bash
# 在网络良好的环境下下载依赖JAR包
# 然后在服务器上手动安装
mvn install:install-file -Dfile=wxpay-sdk-0.0.3.jar -DgroupId=com.github.wxpay -DartifactId=wxpay-sdk -Dversion=0.0.3 -Dpackaging=jar
```

## 二、完整的宝塔面板部署步骤

### 1. 环境准备

- 安装宝塔面板
- 在宝塔面板中安装Java项目环境：
  - JDK 1.8+
  - MySQL 5.7+
  - Redis
  - Nginx

### 2. 数据库配置

1. 在宝塔面板中创建数据库：
   - 数据库名：cashier（根据application.properties中的配置）
   - 用户名：cashier
   - 密码：设置安全密码

2. 导入初始数据库脚本（需要从项目中获取SQL文件）

### 3. 代码部署与编译

1. 上传或克隆代码到服务器：

```bash
git clone [项目Git地址] /www/wwwroot/fuint
cd /www/wwwroot/fuint
```

2. 配置Maven（按照上述Maven配置方案）

3. 解决依赖问题：

检查并添加缺失的依赖到项目根目录的pom.xml文件中：

```bash
vi /www/wwwroot/fuint/pom.xml
```

添加以下依赖（如果不存在）：

```xml
<!-- 微信支付SDK -->
<dependency>
    <groupId>com.github.wxpay</groupId>
    <artifactId>wxpay-sdk</artifactId>
    <version>0.0.3</version>
</dependency>

<!-- 更新Lombok版本 -->
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <version>1.18.20</version>
    <scope>provided</scope>
</dependency>
```

如果无法通过Maven仓库下载依赖，可以手动下载并安装：

```bash
# 下载wxpay-sdk-0.0.3.jar和lombok-1.18.20.jar
# 然后手动安装到本地Maven仓库
mvn install:install-file -Dfile=/path/to/wxpay-sdk-0.0.3.jar -DgroupId=com.github.wxpay -DartifactId=wxpay-sdk -Dversion=0.0.3 -Dpackaging=jar
mvn install:install-file -Dfile=/path/to/lombok-1.18.20.jar -DgroupId=org.projectlombok -DartifactId=lombok -Dversion=1.18.20 -Dpackaging=jar
```

4. 修改数据库连接配置：

```bash
vi /www/wwwroot/fuint/configure/prod/application.properties
```

修改以下配置：

```properties
# 数据库配置
spring.datasource.url=jdbc:mysql://localhost:3306/cashier?useUnicode=true&characterEncoding=UTF8&useSSL=false
spring.datasource.username=cashier
spring.datasource.password=你的数据库密码

# Redis配置
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=你的Redis密码（如果有）
```

5. 编译打包：

```bash
cd /www/wwwroot/fuint
# 使用阿里云Maven镜像加速构建
mvn clean package -Dmaven.test.skip=true -DconnectionRequestTimeout=60000 -DconnectTimeout=60000 -Dsocket.timeout=60000
```

如果编译过程中出现其他依赖错误，请根据错误信息添加相应的依赖或更新版本。

### 4. 部署与启动


1. 创建启动脚本，添加适当的JVM参数和环境变量：

```bash
vi /www/wwwroot/fuint/start.sh
```

添加以下内容：

```bash
#!/bin/bash

# 设置Maven和Java环境变量
export MAVEN_OPTS="-Xms512m -Xmx1024m -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true"
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk  # 根据实际JDK路径修改
export PATH=$JAVA_HOME/bin:$PATH

# 进入项目目录
cd /www/wwwroot/fuint

# 启动应用，添加内存配置和编码设置
nohup java -Xms1g -Xmx2g -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m \
  -Dfile.encoding=UTF-8 \
  -jar fuint-application/target/fuint-application-1.0.0.jar \
  --spring.profiles.active=prod > fuint.log 2>&1 &

# 保存进程ID
echo $! > fuint.pid

echo "Fuint application started with PID $(cat fuint.pid)"
```

2. 创建停止脚本：

```bash
vi /www/wwwroot/fuint/stop.sh
```

添加以下内容：

```bash
#!/bin/bash
PID=$(cat /www/wwwroot/fuint/fuint.pid)
if [ -z "$PID" ]; then
  echo "Application is not running"
else
  echo "Stopping Fuint application with PID $PID"
  kill -15 $PID
  sleep 5
  if ps -p $PID > /dev/null; then
    echo "Force killing process..."
    kill -9 $PID
  fi
  rm -f /www/wwwroot/fuint/fuint.pid
  echo "Application stopped"
fi
```

3. 创建重启脚本：

```bash
vi /www/wwwroot/fuint/restart.sh
```

添加以下内容：

```bash
#!/bin/bash
echo "Restarting Fuint application..."
/www/wwwroot/fuint/stop.sh
sleep 5
/www/wwwroot/fuint/start.sh
echo "Restart complete"
```

4. 设置脚本执行权限：

```bash
chmod +x /www/wwwroot/fuint/start.sh
chmod +x /www/wwwroot/fuint/stop.sh
chmod +x /www/wwwroot/fuint/restart.sh
```

5. 启动应用：

```bash
/www/wwwroot/fuint/start.sh
```

6. 检查应用启动状态：

```bash
# 查看日志输出
tail -f /www/wwwroot/fuint/fuint.log

# 检查进程是否运行
ps -ef | grep fuint
```

### 5. Nginx配置

在宝塔面板中添加网站，配置Nginx反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 6. 常见问题排查

1. 查看应用日志：

```bash
tail -f /www/wwwroot/fuint/fuint.log
```

2. 检查端口占用：

```bash
netstat -tunlp | grep 8080
```

3. 检查防火墙设置：

```bash
# 如果使用firewalld
firewall-cmd --list-all

# 如果使用iptables
iptables -L -n
```

4. 检查SELinux状态：

```bash
getenforce
# 如果是Enforcing，可以临时关闭
setenforce 0
```

## 三、定时任务配置

根据application.properties中的定时任务配置，可以在宝塔面板中设置相应的定时任务：

```properties
# 定时发送消息
message.job.switch = 0
message.job.time = 0 0/1 * * * ?

# 卡券到期处理
couponExpire.job.switch = 0
couponExpire.job.time = 0 0/1 * * * ?

# 订单超时取消
orderCancel.job.switch = 0
orderCancel.job.time = 0 0/1 * * * ?

# 分佣提成计算
commission.job.switch = 1
commission.job.time = 0 0/1 * * * ?

# 微信小程序上传发货信息处理
uploadShippingInfoJob.job.switch = 1
uploadShippingInfoJob.job.time = 0 0/1 * * * ?
```

这些定时任务已经在应用内部配置，只需确保应用正常运行即可。

## 四、系统维护

### 1. 备份策略

在宝塔面板中设置定期备份：
- 数据库备份
- 代码备份
- 配置文件备份

### 2. 监控

可以使用宝塔面板自带的监控功能，或者添加第三方监控工具。

### 3. 日志管理

设置日志轮转，避免日志文件过大：

```bash
vi /etc/logrotate.d/fuint
```

添加以下内容：

```
/www/wwwroot/fuint/fuint.log {
    daily
    rotate 7
    missingok
    dateext
    compress
    notifempty
    copytruncate
}
```

## 五、安全配置

1. 设置合理的文件权限
2. 使用HTTPS协议
3. 配置防火墙，只开放必要端口
4. 定期更新系统和应用
5. 设置强密码策略

## 六、性能优化与问题排查

### 1. JVM参数优化

根据服务器配置，调整启动脚本中的JVM参数：

```bash
# 4G内存的服务器推荐配置
java -Xms1g -Xmx2g -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m \
  -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication \
  -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/www/wwwroot/fuint/logs/heapdump.hprof \
  -Dfile.encoding=UTF-8 \
  -jar fuint-application/target/fuint-application-1.0.0.jar --spring.profiles.active=prod

# 8G及以上内存的服务器推荐配置
java -Xms2g -Xmx4g -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
  -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication \
  -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/www/wwwroot/fuint/logs/heapdump.hprof \
  -Dfile.encoding=UTF-8 \
  -jar fuint-application/target/fuint-application-1.0.0.jar --spring.profiles.active=prod
```

### 2. Maven构建优化

在服务器上创建或修改Maven的settings.xml文件，添加以下配置：

```xml
<settings>
  <!-- 其他配置 -->
  
  <!-- 并行构建 -->
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <fork>true</fork>
          <meminitial>512m</meminitial>
          <maxmem>1024m</maxmem>
        </configuration>
      </plugin>
    </plugins>
  </build>
  
  <!-- 本地仓库路径 -->
  <localRepository>/www/wwwroot/maven-repo</localRepository>
</settings>
```

使用并行构建加速Maven编译：

```bash
mvn clean package -T 4 -Dmaven.test.skip=true
```

### 3. 数据库优化

- 添加必要的索引，特别是查询频繁的字段
- 优化SQL查询，避免全表扫描和复杂连接
- 配置合理的连接池大小：

```properties
# 在application.properties中配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.max-lifetime=1800000
```

### 4. Redis优化

- 设置合理的缓存策略和过期时间
- 配置持久化，防止数据丢失：

```properties
# Redis配置
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.pool.max-wait=-1ms
```

### 5. Nginx优化

- 启用gzip压缩减小传输数据大小：

```nginx
gzip on;
gzip_min_length 1k;
gzip_comp_level 6;
gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
gzip_vary on;
```

- 配置静态资源缓存：

```nginx
location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg)$ {
    expires 7d;
    add_header Cache-Control "public, max-age=604800";
}
```

- 调整worker进程数和连接数：

```nginx
worker_processes auto;
worker_connections 1024;
```

### 6. 常见问题排查

#### Maven依赖问题

如果遇到依赖下载失败或冲突：

```bash
# 清理本地仓库缓存
mvn dependency:purge-local-repository

# 强制更新依赖
mvn clean package -U -Dmaven.test.skip=true

# 分析依赖树，查找冲突
mvn dependency:tree -Dverbose
```

#### 内存溢出问题

如果应用启动后出现OutOfMemoryError：

1. 检查JVM参数配置是否合理
2. 分析堆内存转储文件（使用JVisualVM或MAT工具）
3. 检查代码中是否存在内存泄漏

#### 数据库连接问题

如果出现数据库连接异常：

1. 检查数据库服务是否正常运行
2. 验证数据库用户名和密码是否正确
3. 确认数据库连接URL是否正确
4. 检查防火墙是否允许数据库连接