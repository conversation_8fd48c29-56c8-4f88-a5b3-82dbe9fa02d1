<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.TAccountDutyMapper">
    <select id="getDutyIdsByAccountId" resultType="java.lang.Integer">
        select distinct duty_id from t_account_duty t where t.acct_id = #{accountId}
    </select>

    <delete id="deleteDutiesByAccountId">
        delete from t_account_duty where acct_id = #{accountId}
    </delete>
</mapper>
