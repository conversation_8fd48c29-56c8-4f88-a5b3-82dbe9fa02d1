# \u6570\u636E\u5E93\u914D\u7F6E
spring.datasource.url=***************************************************************************************
spring.datasource.username=cashier
spring.datasource.password=EdxyGZAzDXf86ZfS

# Redis\u914D\u7F6E
spring.session.store-type=redis
spring.session.redis.namespace=fuint
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.redis.database=0 
# Redis\u670D\u52A1\u5668\u5730\u5740(\u751F\u4EA7)
spring.redis.host=127.0.0.1
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u7AEF\u53E3
spring.redis.port=6379
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u5BC6\u7801\uFF08\u9ED8\u8BA4\u4E3A\u7A7A\uFF09
spring.redis.password=
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.pool.max-active=-1
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.pool.max-wait=-1 
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.pool.max-idle=8 
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.pool.min-idle=0 
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.redis.timeout=0

################## \u5B9A\u65F6\u811A\u672C\u914D\u7F6E #########################
# \u5B9A\u65F6\u53D1\u9001\u6D88\u606F
message.job.switch = 0
message.job.time = 0 0/1 * * * ?

# \u5361\u5238\u5230\u671F\u5904\u7406
couponExpire.job.switch = 0
couponExpire.job.time = 0 0/1 * * * ?

# \u8BA2\u5355\u8D85\u65F6\u53D6\u6D88
orderCancel.job.switch = 0
orderCancel.job.time = 0 0/1 * * * ?

# \u5206\u4F63\u63D0\u6210\u8BA1\u7B97
commission.job.switch = 1
commission.job.time = 0 0/1 * * * ?

# \u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\u4E0A\u4F20\u53D1\u8D27\u4FE1\u606F\u5904\u7406
uploadShippingInfoJob.job.switch = 1
uploadShippingInfoJob.job.time = 0 0/1 * * * ?
