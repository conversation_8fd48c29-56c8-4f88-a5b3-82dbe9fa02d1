# Fuint数据库重建指导

## 问题描述
收银台系统中"选择会员"功能显示空白，无法看到会员列表。

## 解决方案

### 方法1：使用MySQL命令行工具

1. **打开命令提示符（CMD）**
   - 按 `Win + R`，输入 `cmd`，回车

2. **找到MySQL安装目录**
   ```cmd
   # 常见路径，根据实际安装路径调整
   cd "C:\Program Files\MySQL\MySQL Server 8.0\bin"
   ```

3. **连接MySQL**
   ```cmd
   mysql -u root -p
   ```
   输入MySQL root密码

4. **执行数据库重建**
   ```sql
   -- 删除现有数据库
   DROP DATABASE IF EXISTS `fuint-db`;
   
   -- 创建新数据库
   CREATE DATABASE `fuint-db` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
   
   -- 使用数据库
   USE `fuint-db`;
   
   -- 导入完整的SQL文件
   SOURCE D:/kaifa/fuint/fuint/db/fuint-db.sql;
   ```

### 方法2：使用图形化工具（推荐）

1. **使用Navicat、phpMyAdmin或MySQL Workbench**

2. **创建新数据库**
   - 数据库名：`fuint-db`
   - 字符集：`utf8`
   - 排序规则：`utf8_general_ci`

3. **导入SQL文件**
   - 选择导入功能
   - 选择文件：`D:\kaifa\fuint\fuint\db\fuint-db.sql`
   - 执行导入

4. **验证导入结果**
   ```sql
   USE `fuint-db`;
   SELECT COUNT(*) FROM mt_user;
   SELECT * FROM mt_user_grade;
   ```

### 方法3：使用我们准备的简化脚本

1. **使用图形化工具执行 `rebuild_database.sql`**
   - 该文件包含基础表结构和测试数据
   - 位置：`D:\kaifa\fuint\rebuild_database.sql`

## 配置修改

### 1. 后端配置文件修改

修改 `fuint/configure/dev/application.properties`：

```properties
# 数据库配置
spring.datasource.url=**********************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=你的MySQL密码
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Redis配置（如果有Redis）
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=
spring.redis.database=0
```

### 2. 收银台配置修改

修改收银台的API配置文件，确保指向正确的后端地址：

```javascript
// 通常在 fuintCashier/src/renderer/utils/request.js 或类似文件中
const baseURL = 'http://localhost:8080/fuint-application'
```

## 验证步骤

1. **重启后端服务**
   ```bash
   # 在后端项目目录下
   mvn clean install
   java -jar fuint-application/target/fuint-application-1.0.0.jar
   ```

2. **重启收银台应用**

3. **测试会员列表**
   - 打开收银台
   - 点击"选择会员"
   - 应该能看到测试会员数据：
     - 张三 (13800138001)
     - 李四 (13800138002)
     - 王五 (13800138003)
     - 赵六 (13800138004)

## 测试数据说明

脚本已自动插入以下测试数据：

### 会员数据
- **张三**: 手机号 13800138001, 余额 100元, 积分 500
- **李四**: 手机号 13800138002, 余额 200.5元, 积分 800
- **王五**: 手机号 13800138003, 余额 50元, 积分 300
- **赵六**: 手机号 13800138004, 余额 0元, 积分 100

### 会员等级
- **普通会员**: 注册即可获得
- **银卡会员**: 累计消费满500元
- **金卡会员**: 累计消费满1000元
- **VIP会员**: 累计消费满2000元

### 管理员账户
- **用户名**: admin
- **密码**: 123456

## 常见问题

1. **如果仍然看不到会员**
   - 检查后端服务是否正常启动
   - 检查数据库连接配置是否正确
   - 查看浏览器开发者工具的网络请求是否成功

2. **如果API请求失败**
   - 确认后端服务端口（通常是8080）
   - 检查防火墙设置
   - 确认收银台的API地址配置

3. **如果数据库连接失败**
   - 确认MySQL服务是否启动
   - 检查用户名密码是否正确
   - 确认数据库名称是否为 `fuint-db`

## 联系支持

如果问题仍然存在，请提供：
1. 错误日志信息
2. 浏览器开发者工具的网络请求截图
3. 后端服务启动日志
