<template>
  <el-dialog
    title="积分变更"
    :visible.sync="dialogVisible"
    width="500px"
    append-to-body
    @close="handleClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="变更类型" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio label="add">增加积分</el-radio>
          <el-radio label="reduce">减少积分</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="积分数量" prop="amount">
        <el-input
          v-model="form.amount"
          type="number"
          placeholder="请输入积分数量"
          clearable
        />
      </el-form-item>
      <el-form-item label="变更原因" prop="reason">
        <el-select v-model="form.reason" placeholder="请选择变更原因">
          <el-option label="管理员调整" value="admin_adjust" />
          <el-option label="消费获得" value="consume_get" />
          <el-option label="活动奖励" value="activity_reward" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'PointRecharge',
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        type: 'add',
        amount: '',
        reason: '',
        remark: ''
      },
      rules: {
        type: [
          { required: true, message: '请选择变更类型', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入积分数量', trigger: 'blur' },
          { pattern: /^[0-9]+$/, message: '请输入正确的积分数量', trigger: 'blur' }
        ],
        reason: [
          { required: true, message: '请选择变更原因', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.showDialog
      },
      set(val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // TODO: 调用积分变更接口
          console.log('积分变更数据:', {
            userId: this.userId,
            ...this.form
          })
          this.$message.success('积分变更成功')
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.$emit('closeDialog', 'point')
      this.resetForm()
    },
    resetForm() {
      this.$refs.form && this.$refs.form.resetFields()
      this.form = {
        type: 'add',
        amount: '',
        reason: '',
        remark: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
