<template>
  <div class="cashier-container">
    <div class="left-panel">
      <div class="title">收银台</div>
      <div class="content">
        <el-form class="form" ref="form" :model="form" label-width="100px">
          <el-form-item class="member-section">
            <div class="member-info" v-if="selectedMember">
              <img class="avatar" v-if="selectedMember.avatar" :src="selectedMember.avatar"/>
              <img class="avatar" v-else src="@/assets/images/avatar.png"/>
              <div class="info">
                <div class="name">{{ selectedMember.name }}</div>
                <div class="mobile">{{ selectedMember.mobile }}</div>
              </div>
              <el-button type="text" @click="handleSelectMember">重新选择</el-button>
            </div>
            <el-button v-else type="primary" class="select-member-btn" @click="handleSelectMember">
              选择会员
            </el-button>
          </el-form-item>

          <el-form-item class="form-item" label="消费金额" prop="amount">
            <el-input
              class="input-item"
              type="number"
              v-model="form.amount"
              placeholder="请输入消费金额"
              clearable
            />
          </el-form-item>

          <el-form-item class="form-item" label="备注信息" prop="remark">
            <el-input
              type="textarea"
              v-model="form.remark"
              placeholder="请输入备注信息"
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="action">
          <el-button type="primary" class="btn-submit" @click="handleSubmit">确认收款</el-button>
        </div>
      </div>
    </div>

    <!-- 会员选择对话框 -->
    <el-dialog
      title="选择会员"
      :visible.sync="memberDialogVisible"
      width="70%"
      append-to-body
    >
      <member-list
        ref="memberList"
        @select="handleMemberSelected"
      />
    </el-dialog>

    <!-- 结算对话框 -->
    <settlement-dialog
      ref="settlement"
      :visible.sync="settlementVisible"
      :member-info="selectedMember"
      :total-price="form.amount"
      :remark="form.remark"
      @submit="handlePaymentSubmit"
    />
  </div>
</template>

<script>
import MemberList from './components/memberList'
import SettlementDialog from './components/settlementDialog'

export default {
  name: 'Cashier',
  components: {
    MemberList,
    SettlementDialog
  },
  data() {
    return {
      form: {
        amount: '',
        remark: ''
      },
      selectedMember: null,
      memberDialogVisible: false,
      settlementVisible: false
    }
  },
  methods: {
    handleSelectMember() {
      this.memberDialogVisible = true
    },
    handleMemberSelected(member) {
      this.selectedMember = member
      this.memberDialogVisible = false
    },
    handleSubmit() {
      const { amount } = this.form
      if (!amount) {
        this.$message.error('请输入消费金额')
        return
      }
      // 打开结算对话框
      this.settlementVisible = true
    },
    handlePaymentSubmit(paymentData) {
      // TODO: 调用支付接口
      console.log('支付数据:', {
        memberId: this.selectedMember?.id,
        ...this.form,
        ...paymentData
      })
      this.$message.success('支付成功')
      this.resetForm()
    },
    resetForm() {
      this.form = {
        amount: '',
        remark: ''
      }
      this.selectedMember = null
      this.settlementVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.cashier-container {
  padding: 20px;
  height: 100%;

  .left-panel {
    width: 500px;
    min-height: 400px;
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);

    .title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
      padding-left: 10px;
      border-left: 4px solid #409EFF;
    }

    .content {
      .form {
        margin-bottom: 30px;

        .member-section {
          margin-bottom: 20px;

          .member-info {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;

            .avatar {
              width: 50px;
              height: 50px;
              border-radius: 25px;
              margin-right: 15px;
            }

            .info {
              flex-grow: 1;

              .name {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 5px;
              }

              .mobile {
                color: #666;
                font-size: 14px;
              }
            }
          }

          .select-member-btn {
            width: 100%;
          }
        }

        .form-item {
          margin-bottom: 20px;
        }
      }

      .action {
        text-align: center;
        .btn-submit {
          width: 200px;
        }
      }
    }
  }
}
</style>
