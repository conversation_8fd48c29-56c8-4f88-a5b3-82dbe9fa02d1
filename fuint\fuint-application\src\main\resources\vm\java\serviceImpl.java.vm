package com.fuint.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuint.framework.annoation.OperationServiceLog;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.framework.pagination.PaginationRequest;
import com.fuint.framework.pagination.PaginationResponse;
import com.fuint.repository.model.${className};
import com.fuint.common.service.${tableClass}Service;
import com.fuint.common.enums.StatusEnum;
import com.fuint.repository.mapper.${className}Mapper;
import com.github.pagehelper.PageHelper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.github.pagehelper.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;

/**
 * ${moduleName}服务接口
 *
 * Created by ${author}
 * CopyRight https://www.fuint.cn
 */
@Service
@AllArgsConstructor
public class ${tableClass}ServiceImpl extends ServiceImpl<${className}Mapper, ${className}> implements ${tableClass}Service {

    private static final Logger logger = LoggerFactory.getLogger(${tableClass}ServiceImpl.class);

    private ${className}Mapper ${tablePrefix}${tableClass}Mapper;

    /**
     * 分页查询数据列表
     *
     * @param paginationRequest
     * @return
     */
    @Override
    public PaginationResponse<${className}> query${tableClass}ListByPagination(PaginationRequest paginationRequest) {
        Page<${className}> pageHelper = PageHelper.startPage(paginationRequest.getCurrentPage(), paginationRequest.getPageSize());
        LambdaQueryWrapper<${className}> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.ne(${className}::getStatus, StatusEnum.DISABLE.getKey());

        lambdaQueryWrapper.orderByAsc(${className}::getId);
        List<${className}> dataList = ${tablePrefix}${tableClass}Mapper.selectList(lambdaQueryWrapper);

        PageRequest pageRequest = PageRequest.of(paginationRequest.getCurrentPage(), paginationRequest.getPageSize());
        PageImpl pageImpl = new PageImpl(dataList, pageRequest, pageHelper.getTotal());
        PaginationResponse<${className}> paginationResponse = new PaginationResponse(pageImpl, ${className}.class);
        paginationResponse.setTotalPages(pageHelper.getPages());
        paginationResponse.setTotalElements(pageHelper.getTotal());
        paginationResponse.setContent(dataList);

        return paginationResponse;
    }

    /**
     * 添加${moduleName}
     *
     * @param ${tablePrefix}${tableClass} ${moduleName}信息
     * @return
     */
    @Override
    @OperationServiceLog(description = "新增${moduleName}")
    public ${className} add${tableClass}(${className} ${tablePrefix}${tableClass}) throws BusinessCheckException {
        ${tablePrefix}${tableClass}.setStatus(StatusEnum.ENABLED.getKey());
        ${tablePrefix}${tableClass}.setUpdateTime(new Date());
        ${tablePrefix}${tableClass}.setCreateTime(new Date());
        Integer id = ${tablePrefix}${tableClass}Mapper.insert(${tablePrefix}${tableClass});
        if (id > 0) {
            return ${tablePrefix}${tableClass};
        } else {
            throw new BusinessCheckException("新增${moduleName}数据失败");
        }
    }

    /**
     * 根据ID获${moduleName}取息
     *
     * @param id ${moduleName}ID
     * @return
     */
    @Override
    public ${className} query${tableClass}ById(Integer id) {
        return ${tablePrefix}${tableClass}Mapper.selectById(id);
    }

    /**
     * 根据ID删除${moduleName}
     *
     * @param id ${moduleName}ID
     * @param operator 操作人
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationServiceLog(description = "删除${moduleName}")
    public void delete${tableClass}(Integer id, String operator) {
        ${className} ${tablePrefix}${tableClass} = query${tableClass}ById(id);
        if (null == ${tablePrefix}${tableClass}) {
            return;
        }
        ${tablePrefix}${tableClass}.setStatus(StatusEnum.DISABLE.getKey());
        ${tablePrefix}${tableClass}.setUpdateTime(new Date());
        ${tablePrefix}${tableClass}Mapper.updateById(${tablePrefix}${tableClass});
    }

    /**
     * 修改${moduleName}数据
     *
     * @param ${tablePrefix}${tableClass}
     * @throws BusinessCheckException
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationServiceLog(description = "更新${moduleName}")
    public ${className} update${tableClass}(${className} ${tablePrefix}${tableClass}) throws BusinessCheckException {
        ${tablePrefix}${tableClass} = query${tableClass}ById(${tablePrefix}${tableClass}.getId());
        if (${tablePrefix}${tableClass} == null) {
            throw new BusinessCheckException("该${moduleName}状态异常");
        }
        ${tablePrefix}${tableClass}.setUpdateTime(new Date());
        ${tablePrefix}${tableClass}Mapper.updateById(${tablePrefix}${tableClass});
        return ${tablePrefix}${tableClass};
    }

   /**
    * 根据条件搜索${moduleName}
    *
    * @param  params 查询参数
    * @throws BusinessCheckException
    * @return
    * */
    @Override
    public List<${className}> query${tableClass}ListByParams(Map<String, Object> params) {
        String status =  params.get("status") == null ? StatusEnum.ENABLED.getKey(): params.get("status").toString();
        String storeId =  params.get("storeId") == null ? "" : params.get("storeId").toString();
        String merchantId =  params.get("merchantId") == null ? "" : params.get("merchantId").toString();

        LambdaQueryWrapper<${className}> lambdaQueryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(status)) {
            lambdaQueryWrapper.eq(${className}::getStatus, status);
        }
        if (StringUtils.isNotBlank(merchantId)) {
            lambdaQueryWrapper.eq(${className}::getMerchantId, merchantId);
        }
        if (StringUtils.isNotBlank(storeId)) {
            lambdaQueryWrapper.and(wq -> wq
                    .eq(${className}::getStoreId, 0)
                    .or()
                    .eq(${className}::getStoreId, storeId));
        }

        lambdaQueryWrapper.orderByAsc(${className}::getId);
        List<${className}> dataList = ${tablePrefix}${tableClass}Mapper.selectList(lambdaQueryWrapper);
        return dataList;
    }
}
