<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtUserGroupMapper">
    <select id="getMemberNum" resultType="java.lang.Long">
        select count(0) from mt_user t where t.STATUS != 'D'
        <if test="groupIds != null and groupIds.size > 0">
            AND t.GROUP_ID IN
            <foreach collection="groupIds" item="groupId" index="index" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
    </select>
</mapper>
